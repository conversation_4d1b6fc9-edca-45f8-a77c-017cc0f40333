import { useQuery } from 'react-apollo';
import { useCallback } from 'react';
import useSocket<PERSON>andler from '../socket/useSocketHandler';
import unreadMessagesCountQuery from './data/unreadMessagesCount.graphql';

export default function useUnreadMessagesCount(
  messageId: number,
): {
  unreadMessagesCount: number | undefined;
  refetch: () => void;
} {
  const { data: { unreadMessagesCount } = {}, refetch } = useQuery<{
    unreadMessagesCount: number;
  }>(unreadMessagesCountQuery, {
    variables: { messageId },
    skip: !messageId,
  });

  const handleMessage = useCallback(
    (message: any) => {
      if (message.messageId === messageId) {
        refetch();
      }
    },
    [messageId, refetch],
  );

  useSocketHandler('message', handleMessage);

  return {
    unreadMessagesCount,
    refetch,
  };
}
